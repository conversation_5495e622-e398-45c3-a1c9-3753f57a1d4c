"use server";
import { getAccessToken } from "@auth0/nextjs-auth0";
import axios from "axios";
import { cookies } from "next/headers";
// TypeScript interfaces for concept testing data
interface ConceptTestData {
  run_details: {
    summary: {
      "Experiment Mappings": {
        concept_description: string;
        concept_statements: Array<{
          labels: string[];
          statement: string;
        }>;
        image_description: string;
        image_name: string;
      };
      "External Persona Provided": boolean;
      "Survey Analytics": {
        survey_results: Array<{
          avg_score: number;
          net_sentiment: number;
          responses: Record<string, number>;
          statement: string;
        }>;
      };
    };
    configs: {
      user: string;
      wandb_id: string;
      wandb_name: string;
      experiment_design: {
        year: string;
        country: string;
        image_name: string;
        is_private: boolean;
        expr_llm_model: string;
        experiment_type: string;
        population_traits: Record<string, string[]>;
        target_population?: {
          age: [number, number];
          state?: string;
          gender: string[];
          racial_group: string[];
          education_level: string[];
          household_income: [number, number];
          number_of_children: string[];
        };
        concept_statements: Array<{
          labels: string[];
          statement: string;
        }>;
        concept_description: string;
      };
    };
    run_state: string;
    run_id: string;
    run_name: string;
    tags: string[];
    start_time: string;
  };
}

// Server action to fetch concept test data
export async function getConceptTestData(
  runId: string
): Promise<ConceptTestData | null> {
  try {
    // Handle cookies properly for server components
    await cookies(); // Await cookies before using getAccessToken

    // Get access token
    const { accessToken } = await getAccessToken();

    if (!accessToken) {
      console.error("No access token available");
      return null;
    }

    // Ensure backend endpoint is configured
    const backendEndpoint = process.env.NEXT_PUBLIC_BACKEND_ENDPOINT;
    if (!backendEndpoint) {
      console.error("NEXT_PUBLIC_BACKEND_ENDPOINT is not configured");
      return null;
    }

    const apiUrl = `${backendEndpoint}/api/v1/runs/${runId}`;
    console.log("Making request to:", apiUrl);

    // Make direct API call to backend
    const response = await axios.get(apiUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      timeout: 30_000, // 30 seconds
      // Skip SSL verification in development to handle certificate mismatches
      httpsAgent:
        process.env.NODE_ENV === "development"
          ? new (require("https").Agent)({ rejectUnauthorized: false })
          : undefined,
    });

    console.log("API Response status:", response.status);
    console.log("API Response data:", response.data);

    // Check if response is successful
    if (response.status === 200 && response.data) {
      return response.data;
    } else {
      console.error("API returned unsuccessful response:", response.status);
      return null;
    }
  } catch (error: unknown) {
    console.error("Concept Test Data Error:", error);

    // Handle different error types
    if (axios.isAxiosError(error)) {
      console.error("Axios error details:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message,
        url: error.config?.url,
      });
    }

    return null;
  }
}
