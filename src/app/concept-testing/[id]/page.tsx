// "use client"
import React from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft, Download, Share2, Users } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import HeaderClient from "./HeaderClient";
import ConceptImage from "./ConceptImage";
import { SurveyResultsChart } from "@/components/SurveyResultsChart";
import { NetSentimentChart } from "@/components/NetSentimentChart";
import { getConceptTestData } from "@/app/actions/concept-testing/getConceptTestData";

// Helper function to get country flag URL
function getCountryFlag(countryName: string): string {
  const countryCodeMap: Record<string, string> = {
    "United States of America (USA)": "US",
    "United Kingdom": "GB",
    Canada: "CA",
    Australia: "AU",
    Germany: "DE",
    France: "FR",
    Japan: "JP",
    Brazil: "BR",
    India: "IN",
    China: "CN",
  };

  const code = countryCodeMap[countryName] || "US";
  return `http://purecatamphetamine.github.io/country-flag-icons/3x2/${code}.svg`;
}

// Helper function to calculate total respondents
function calculateTotalRespondents(responses: Record<string, number>): number {
  return Object.values(responses).reduce((sum, count) => sum + count, 0);
}

// Helper function to construct S3 URL
function getS3ImageUrl(imageName: string): string {
  const region = process.env.AWS_REGION || "us-east-1";
  const bucketName = process.env.AWS_S3_BUCKET_NAME;

  if (!bucketName) {
    console.warn("AWS_S3_BUCKET_NAME not configured");
    return "/brain_logo.png";
  }

  const hostname =
    region === "us-east-1"
      ? `${bucketName}.s3.amazonaws.com`
      : `${bucketName}.s3.${region}.amazonaws.com`;

  return `https://${hostname}/${imageName}`;
}

// Helper function to transform backend survey data to chart format
function transformSurveyDataForCharts(
  surveyResults: Array<{
    avg_score: number;
    net_sentiment: number;
    responses: Record<string, number>;
    statement: string;
  }>
) {
  return surveyResults.map((result) => ({
    statement: result.statement,
    net_sentiment: result.net_sentiment,
    responses: {
      "Strongly Disagree": result.responses["Strongly Disagree"] || 0,
      Disagree: result.responses["Disagree"] || 0,
      Neutral: result.responses["Neutral"] || 0,
      Agree: result.responses["Agree"] || 0,
      "Strongly Agree": result.responses["Strongly Agree"] || 0,
    },
  }));
}

// Server Component
async function ConceptTestPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
 console.log("Fetching concept test data for ID:", id);
  // Fetch real data from backend using server action
  const conceptTestData = await getConceptTestData(id);
  console.log("🚀 ~ conceptTestData:", conceptTestData)

  if (!conceptTestData) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-semibold text-text-dark mb-2">
              Concept Test Not Found
            </h1>
            <p className="text-muted-foreground mb-4">
              The concept test you're looking for doesn't exist or couldn't be
              loaded.
            </p>
            <Link
              href="/concept-testing/results"
              className="inline-flex items-center text-primary hover:text-primary/80 transition-colors"
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Back to Tests
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Extract data from the backend response
  const { run_details } = conceptTestData;
  const { summary, configs } = run_details;
  const experimentMappings = summary["Experiment Mappings"];
  const surveyAnalytics = summary["Survey Analytics"];
  const experimentDesign = configs.experiment_design;

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Calculate total respondents from first survey result
  const totalRespondents =
    surveyAnalytics.survey_results.length > 0
      ? calculateTotalRespondents(surveyAnalytics.survey_results[0].responses)
      : 0;

  // Transform survey data for charts
  const transformedSurveyData = transformSurveyDataForCharts(
    surveyAnalytics.survey_results
  );

  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Top navigation bar */}
      <div className="sticky top-0 z-10 bg-background border-b border-border">
        <div className="container mx-auto px-4 sm:px-6 py-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Link
              href="/concept-testing/results"
              className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              <span>Back to Tests</span>
            </Link>
          </div>
          <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
            <HeaderClient />
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 container mx-auto px-4 sm:px-6 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-semibold text-text-dark">
              {run_details.run_name}
            </h1>
            <div className="flex items-center gap-2 mt-2 text-muted-foreground">
              <span>{formatDate(run_details.start_time)}</span>
              <span className="inline-block h-1 w-1 rounded-full bg-muted-foreground"></span>
              <span className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {totalRespondents} respondents
              </span>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" className="h-10 gap-2">
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" className="h-10 gap-2">
              <Share2 className="h-4 w-4" />
              Share
            </Button>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Concept Overview */}
            <div className="bg-card rounded-lg border border-card-border p-6">
              <h2 className="text-xl font-semibold text-text-dark mb-4">
                Concept Overview
              </h2>

              {/* Concept Image */}
              {experimentMappings.image_name && (
                <div className="mb-6">
                  <div className="relative w-full h-64 bg-gray-100 rounded-lg overflow-hidden">
                    <ConceptImage
                      src={getS3ImageUrl(experimentMappings.image_name)}
                      alt="Concept Image"
                      className="object-cover"
                    />
                  </div>
                </div>
              )}

              {/* Concept Description */}
              <div className="mb-6">
                <h3 className="text-lg font-medium text-text-dark mb-2">
                  Description
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {experimentMappings.concept_description}
                </p>
              </div>

              {/* Image Description */}
              {experimentMappings.image_description && (
                <div>
                  <h3 className="text-lg font-medium text-text-dark mb-2">
                    Image Analysis
                  </h3>
                  <p className="text-muted-foreground leading-relaxed whitespace-pre-line">
                    {experimentMappings.image_description}
                  </p>
                </div>
              )}
            </div>

            {/* Survey Results */}
            <div className="bg-card rounded-lg border border-card-border p-6">
              <h2 className="text-xl font-semibold text-text-dark mb-6">
                Survey Results
              </h2>

              {/* Survey Results Charts */}
              <div className="space-y-8">
                {transformedSurveyData.map((result, index) => {
                  const originalResult = surveyAnalytics.survey_results[index];
                  return (
                    <div key={index} className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium text-text-dark">
                          {result.statement}
                        </h3>
                        <div className="text-right">
                          <div className="text-sm text-muted-foreground">
                            Average Score
                          </div>
                          <div className="text-xl font-semibold text-text-dark">
                            {originalResult.avg_score.toFixed(1)}
                          </div>
                        </div>
                      </div>

                      {/* Survey Results Chart */}
                      <SurveyResultsChart data={[result]} />

                      {/* Net Sentiment */}
                      <div className="flex items-center justify-between pt-2">
                        <span className="text-sm text-muted-foreground">
                          Net Sentiment
                        </span>
                        <span
                          className={`text-sm font-medium ${
                            result.net_sentiment > 0
                              ? "text-green-600"
                              : result.net_sentiment < 0
                                ? "text-red-600"
                                : "text-gray-600"
                          }`}
                        >
                          {result.net_sentiment > 0 ? "+" : ""}
                          {result.net_sentiment}%
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Study Details */}
            <div className="bg-card rounded-lg border border-card-border p-6">
              <h3 className="text-lg font-semibold text-text-dark mb-4">
                Study Details
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Year</span>
                  <span className="text-text-dark">
                    {experimentDesign.year}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Country</span>
                  <div className="flex items-center gap-2">
                    <Image
                      src={getCountryFlag(experimentDesign.country)}
                      alt={experimentDesign.country}
                      width={20}
                      height={15}
                      className="rounded-sm"
                    />
                    <span className="text-text-dark">
                      {experimentDesign.country}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Respondents</span>
                  <span className="text-text-dark">{totalRespondents}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status</span>
                  <span className="text-green-600 capitalize">
                    {run_details.run_state}
                  </span>
                </div>
              </div>
            </div>

            {/* Net Sentiment Overview */}
            <div className="bg-card rounded-lg border border-card-border p-6">
              <h3 className="text-lg font-semibold text-text-dark mb-4">
                Net Sentiment Overview
              </h3>
              <NetSentimentChart data={transformedSurveyData} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ConceptTestPage;
