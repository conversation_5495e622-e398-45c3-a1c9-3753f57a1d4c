"use client";

import React from "react";
import Image from "next/image";

interface ConceptImageProps {
  src: string;
  alt: string;
  className?: string;
}

export default function ConceptImage({ src, alt, className }: ConceptImageProps) {
  return (
    <Image
      src={src}
      alt={alt}
      fill
      className={className}
      onError={(e) => {
        e.currentTarget.src = "/brain_logo.png";
      }}
    />
  );
}
