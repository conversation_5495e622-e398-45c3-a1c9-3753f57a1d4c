import React from "react";

interface SurveyResponse {
  "Strongly Disagree": number;
  Disagree: number;
  Neutral: number;
  Agree: number;
  "Strongly Agree": number;
}

interface SurveyResult {
  statement: string;
  responses: SurveyResponse;
  net_sentiment: number;
}

interface SurveyResultsChartProps {
  data: SurveyResult[];
}

export const SurveyResultsChart: React.FC<SurveyResultsChartProps> = ({
  data,
}) => {
  const getPercentage = (value: number, total: number) => {
    return (value / total) * 100;
  };

  const colors = {
    "Strongly Disagree": "#EF4444", // red-500
    Disagree: "#FB923C", // orange-400
    Neutral: "#D1D5DB", // gray-300
    Agree: "#34D399", // emerald-400
    "Strongly Agree": "#10B981", // emerald-500
  };

  const legend = [
    { label: "Strongly Disagree", color: colors["Strongly Disagree"] },
    { label: "Disagree", color: colors["Disagree"] },
    { label: "Neutral", color: colors["Neutral"] },
    { label: "Strongly Agree", color: colors["Strongly Agree"] },
  ];

  return (
    <div className="bg-gray-100 p-8 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-800 mb-12 text-center">
          SURVEY RESULTS
        </h1>

        <div className="bg-white rounded-lg shadow-lg p-8">
          {/* Y-axis labels */}
          <div className="mb-8">
            {/* X-axis percentage labels */}
            <div className="flex justify-between mb-4 text-sm text-gray-600 ml-32">
              <span>0%</span>
              <span>20%</span>
              <span>40%</span>
              <span>60%</span>
              <span>80%</span>
              <span>100%</span>
            </div>

            {data.map((item, index) => {
              const total = Object.values(item.responses).reduce(
                (sum, val) => sum + val,
                0
              );
              let cumulativePercentage = 0;

              return (
                <div key={index} className="flex items-center mb-6">
                  {/* Statement label */}
                  <div className="w-32 text-right pr-4 text-gray-700 font-medium">
                    {item.statement}
                  </div>

                  {/* Stacked bar */}
                  <div className="flex-1 relative">
                    <div className="flex h-12 bg-gray-200 rounded">
                      {Object.entries(item.responses).map(
                        ([category, value]) => {
                          const percentage = getPercentage(value, total);
                          cumulativePercentage += percentage;

                          return (
                            <div
                              key={category}
                              className="flex items-center justify-center text-white text-xs font-medium"
                              style={{
                                width: `${percentage}%`,
                                backgroundColor:
                                  colors[category as keyof SurveyResponse],
                              }}
                            >
                              {percentage > 8
                                ? `${Math.round(percentage)}%`
                                : ""}
                            </div>
                          );
                        }
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Legend */}
          <div className="flex justify-center gap-8 mt-8">
            {legend.map((item) => (
              <div key={item.label} className="flex items-center gap-2">
                <div
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm text-gray-700">{item.label}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
